/**
 * Control Tab Script Bundle
 *
 * Standalone control tab functionality bundle that provides CDP management
 * and basic control tab operations without interceptors.
 *
 * This bundle can be used independently for simple control tab scenarios
 * or as a base for more complex combined bundles.
 */

import { ControlTabScript } from '../control/control-tab-script';
import { CDPManager } from '../control/cdp-manager';
import { interceptorRegistry } from '../interceptors/registry';

// Export classes for global access
export { ControlTabScript, CDPManager, interceptorRegistry };

// Make available globally for injection scripts
if (typeof window !== 'undefined') {
  (window as any).ControlTabScript = ControlTabScript;
  (window as any).CDPManager = CDPManager;
  (window as any).interceptorRegistry = interceptorRegistry;
}

// Auto-initialization function for template-based injection
export function initializeControlTabScript(
  wsEndpoint: string,
  webClientId?: string,
  debug: boolean = false,
): ControlTabScript {
  const config = {
    wsEndpoint,
    webClientId,
    debug,
  };

  const controlScript = new ControlTabScript(config);

  // Auto-initialize
  controlScript.init().catch((error) => {
    console.error('[ControlTabScriptBundle] Auto-initialization failed:', error);
  });

  return controlScript;
}

// Make initialization function available globally
if (typeof window !== 'undefined') {
  (window as any).initializeControlTabScript = initializeControlTabScript;
}
