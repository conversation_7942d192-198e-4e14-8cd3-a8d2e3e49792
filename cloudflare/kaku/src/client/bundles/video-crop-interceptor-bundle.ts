/**
 * Video Crop Interceptor Bundle
 *
 * Standalone video crop interceptor bundle that provides frame cropping
 * and subscription capabilities for video streams.
 *
 * This bundle can be used independently or combined with other interceptors.
 */

import BaseInterceptor from '../interceptors/base-interceptor';
import { VideoFrameInterceptor } from '../interceptors/video-crop-interceptor';
import { interceptorRegistry } from '../interceptors/registry';
import { InterceptorPipeline } from '../interceptors/interceptor-pipeline';
import type { VideoCropConfig } from '../types';

// Register the interceptor
interceptorRegistry.register('video-crop', VideoFrameInterceptor, {
  debug: false,
  enabled: true,
  enableCropping: true,
  frameRate: 30,
});

// Export classes for global access
export { BaseInterceptor, VideoFrameInterceptor, interceptorRegistry, InterceptorPipeline };

// Make available globally for injection scripts
if (typeof window !== 'undefined') {
  (window as any).BaseInterceptor = BaseInterceptor;
  (window as any).VideoFrameInterceptor = VideoFrameInterceptor;
  (window as any).interceptorRegistry = interceptorRegistry;
  (window as any).InterceptorPipeline = InterceptorPipeline;
}

// Factory function for creating video crop interceptor
export function createVideoCropInterceptor(
  name: string = 'video-crop',
  config: VideoCropConfig = {},
): VideoFrameInterceptor {
  return new VideoFrameInterceptor(name, config);
}

// Auto-initialization function for template-based injection
export function initializeVideoCropInterceptor(
  webClientId: string,
  config: VideoCropConfig = {},
): VideoFrameInterceptor[] {
  // Set client configuration
  interceptorRegistry.setClientConfiguration(webClientId, ['video-crop'], {
    'video-crop': config,
  });

  // Create interceptor instances
  return interceptorRegistry.createClientInterceptors(webClientId);
}

// Make functions available globally
if (typeof window !== 'undefined') {
  (window as any).createVideoCropInterceptor = createVideoCropInterceptor;
  (window as any).initializeVideoCropInterceptor = initializeVideoCropInterceptor;
}
