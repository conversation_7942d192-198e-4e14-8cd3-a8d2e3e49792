/**
 * Change Detector Interceptor Bundle
 *
 * Standalone change detector interceptor bundle that provides screen change
 * detection and stream pause/resume functionality.
 *
 * This bundle can be used independently or combined with other interceptors.
 */

import BaseInterceptor from '../interceptors/base-interceptor';
import { ChangeDetectorInterceptor } from '../interceptors/change-detector-interceptor';
import { interceptorRegistry } from '../interceptors/registry';
import { InterceptorPipeline } from '../interceptors/interceptor-pipeline';
import type { ChangeDetectorConfig } from '../types';

// Register the interceptor
interceptorRegistry.register('change-detector', ChangeDetectorInterceptor, {
  debug: false,
  enabled: true,
  changeThreshold: 0.1,
  stabilityThreshold: 0.05,
  consecutiveStableFrames: 3,
  maxWaitDuration: 10000,
  comparisonInterval: 500,
  pixelSampling: 0.1,
});

// Export classes for global access
export { BaseInterceptor, ChangeDetectorInterceptor, interceptorRegistry, InterceptorPipeline };

// Make available globally for injection scripts
if (typeof window !== 'undefined') {
  (window as any).BaseInterceptor = BaseInterceptor;
  (window as any).ChangeDetectorInterceptor = ChangeDetectorInterceptor;
  (window as any).interceptorRegistry = interceptorRegistry;
  (window as any).InterceptorPipeline = InterceptorPipeline;
}

// Factory function for creating change detector interceptor
export function createChangeDetectorInterceptor(
  name: string = 'change-detector',
  config: ChangeDetectorConfig = {},
): ChangeDetectorInterceptor {
  return new ChangeDetectorInterceptor(name, config);
}

// Auto-initialization function for template-based injection
export function initializeChangeDetectorInterceptor(
  webClientId: string,
  config: ChangeDetectorConfig = {},
): ChangeDetectorInterceptor[] {
  // Set client configuration
  interceptorRegistry.setClientConfiguration(webClientId, ['change-detector'], {
    'change-detector': config,
  });

  // Create interceptor instances
  return interceptorRegistry.createClientInterceptors(webClientId);
}

// Make functions available globally
if (typeof window !== 'undefined') {
  (window as any).createChangeDetectorInterceptor = createChangeDetectorInterceptor;
  (window as any).initializeChangeDetectorInterceptor = initializeChangeDetectorInterceptor;
}
