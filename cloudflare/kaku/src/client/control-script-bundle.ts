import './control/control-tab-manager.js';
import './interceptors/registry.js';
import './interceptors/base-interceptor.js';
import './interceptors/video-crop-interceptor.js';
import './interceptors/change-detector-interceptor.js';

// Import and expose CDP Manager globally
import CDPManager from './control/cdp-manager';

// Make CDPManager available on window object for control-tab-manager
window.CDPManager = CDPManager;
