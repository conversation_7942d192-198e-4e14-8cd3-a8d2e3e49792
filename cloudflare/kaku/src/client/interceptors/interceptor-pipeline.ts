/**
 * Interceptor Pipeline
 *
 * Manages a sequential pipeline of video frame interceptors.
 * Provides a unified interface for processing video frames through
 * multiple interceptors in a configurable order.
 *
 * Features:
 * - Sequential interceptor processing
 * - Dynamic interceptor management
 * - Configuration updates
 * - Performance monitoring
 * - Error handling and fallback
 */

import type {
  BaseInterceptorInterface,
  InterceptorPipelineInterface,
  InterceptorConfig,
} from '../types';

export class InterceptorPipeline implements InterceptorPipelineInterface {
  public readonly interceptors: BaseInterceptorInterface[] = [];
  public isInitialized: boolean = false;
  public isEnabled: boolean = true;

  // Performance tracking
  public readonly stats = {
    framesProcessed: 0,
    totalProcessingTime: 0,
    averageProcessingTime: 0,
    interceptorStats: new Map<
      string,
      {
        framesProcessed: number;
        totalProcessingTime: number;
        averageProcessingTime: number;
      }
    >(),
  };

  // Track references
  private originalTrack: MediaStreamTrack | null = null;
  private processedTrack: MediaStreamTrack | null = null;

  constructor(
    interceptors: BaseInterceptorInterface[] = [],
    controller?: InterceptorPipelineInterface,
  ) {
    // Add provided interceptors
    interceptors.forEach((interceptor) => this.addInterceptor(interceptor));

    this.log('InterceptorPipeline created with', this.interceptors.length, 'interceptors');
  }

  /**
   * Initialize the pipeline with a video track
   */
  async initialize(videoTrack: MediaStreamTrack): Promise<MediaStreamTrack> {
    if (!videoTrack || videoTrack.kind !== 'video') {
      throw new Error('InterceptorPipeline requires a valid video track');
    }

    this.originalTrack = videoTrack;
    this.log('Initializing pipeline with', this.interceptors.length, 'interceptors');

    try {
      let currentTrack = videoTrack;

      // Initialize each interceptor in sequence
      for (const interceptor of this.interceptors) {
        if (interceptor.isEnabled) {
          currentTrack = await interceptor.initialize(currentTrack);
          this.log(`Initialized interceptor: ${interceptor.name}`);
        } else {
          this.log(`Skipped disabled interceptor: ${interceptor.name}`);
        }
      }

      this.processedTrack = currentTrack;
      this.isInitialized = true;

      this.log('Pipeline initialized successfully');
      return this.processedTrack;
    } catch (error) {
      this.log('Error initializing pipeline:', error);
      throw error;
    }
  }

  /**
   * Add an interceptor to the pipeline
   */
  addInterceptor(interceptor: BaseInterceptorInterface): void {
    if (!interceptor) {
      throw new Error('Cannot add null or undefined interceptor');
    }

    // Check for duplicate names
    const existingInterceptor = this.interceptors.find((i) => i.name === interceptor.name);
    if (existingInterceptor) {
      this.log(`Warning: Interceptor with name '${interceptor.name}' already exists`);
      return;
    }

    this.interceptors.push(interceptor);
    this.stats.interceptorStats.set(interceptor.name, {
      framesProcessed: 0,
      totalProcessingTime: 0,
      averageProcessingTime: 0,
    });

    this.log(`Added interceptor: ${interceptor.name}`);
  }

  /**
   * Remove an interceptor from the pipeline
   */
  removeInterceptor(name: string): boolean {
    const index = this.interceptors.findIndex((i) => i.name === name);
    if (index === -1) {
      this.log(`Interceptor '${name}' not found in pipeline`);
      return false;
    }

    const interceptor = this.interceptors[index];
    this.interceptors.splice(index, 1);
    this.stats.interceptorStats.delete(name);

    // Cleanup the removed interceptor
    interceptor.cleanup().catch((error) => {
      this.log(`Error cleaning up interceptor '${name}':`, error);
    });

    this.log(`Removed interceptor: ${name}`);
    return true;
  }

  /**
   * Update configuration for a specific interceptor
   */
  updateInterceptorConfig(name: string, config: Partial<InterceptorConfig>): boolean {
    const interceptor = this.interceptors.find((i) => i.name === name);
    if (!interceptor) {
      this.log(`Interceptor '${name}' not found for config update`);
      return false;
    }

    interceptor.updateConfig(config);
    this.log(`Updated config for interceptor: ${name}`);
    return true;
  }

  /**
   * Update configurations for multiple interceptors
   */
  public readonly updateInterceptorConfigs = (configs: Record<string, any>): void => {
    for (const [name, config] of Object.entries(configs)) {
      this.updateInterceptorConfig(name, config);
    }
  };

  /**
   * Enable the pipeline
   */
  enable(): void {
    this.isEnabled = true;
    this.log('Pipeline enabled');
  }

  /**
   * Disable the pipeline
   */
  disable(): void {
    this.isEnabled = false;
    this.log('Pipeline disabled');
  }

  /**
   * Get interceptor by name
   */
  getInterceptor(name: string): BaseInterceptorInterface | undefined {
    return this.interceptors.find((i) => i.name === name);
  }

  /**
   * Get all interceptor names
   */
  getInterceptorNames(): string[] {
    return this.interceptors.map((i) => i.name);
  }

  /**
   * Check if pipeline has a specific interceptor
   */
  hasInterceptor(name: string): boolean {
    return this.interceptors.some((i) => i.name === name);
  }

  /**
   * Cleanup all interceptors and resources
   */
  async cleanup(): Promise<void> {
    this.log('Cleaning up pipeline...');

    // Cleanup all interceptors
    const cleanupPromises = this.interceptors.map((interceptor) =>
      interceptor.cleanup().catch((error) => {
        this.log(`Error cleaning up interceptor '${interceptor.name}':`, error);
      }),
    );

    await Promise.all(cleanupPromises);

    // Clear references
    this.interceptors.length = 0;
    this.stats.interceptorStats.clear();
    this.originalTrack = null;
    this.processedTrack = null;
    this.isInitialized = false;

    this.log('Pipeline cleanup completed');
  }

  /**
   * Get pipeline statistics
   */
  getStats() {
    return {
      ...this.stats,
      interceptorStats: new Map(this.stats.interceptorStats),
    };
  }

  /**
   * Logging utility
   */
  private log(...args: any[]): void {
    console.log('[InterceptorPipeline]', ...args);
  }
}

// Export for use in other modules
export default InterceptorPipeline;

// Make available globally for injection scripts
if (typeof window !== 'undefined') {
  (window as any).InterceptorPipeline = InterceptorPipeline;
}
