import {
  BoundingBox,
  CaptchaDetectorConfig,
  ComparisonResult,
  Config,
  Detection,
  DetectionResults,
  ImageData as IImageData,
  InputEventData,
  ScreenshotComparisonOptions,
  ScreenshotResult,
  Viewport,
} from '../../common/types/';

// Global Window Extensions
declare global {
  interface Window {
    controlTabInjected?: boolean;
    targetTabInjected?: boolean;
    controlTabManagerInjected?: boolean;
    CDPManager?: typeof import('../control/cdp-manager').default;
    interceptorRegistry?: typeof import('../interceptors/registry').interceptorRegistry;
    InterceptorPipeline?: new (
      interceptors?: BaseInterceptorInterface[],
      controller?: InterceptorPipelineInterface,
    ) => InterceptorPipelineInterface;
    BaseInterceptor?: new (
      name: string,
      defaultConfig?: InterceptorConfig,
    ) => BaseInterceptorInterface;
    VideoFrameInterceptor?: new (
      name?: string,
      options?: VideoCropConfig,
    ) => BaseInterceptorInterface;
    BrightnessInterceptor?: new (
      name?: string,
      options?: BrightnessConfig,
    ) => BaseInterceptorInterface;
    controlTabManager?: any;
    BlurInterceptor?: new (name?: string, options?: BlurConfig) => BaseInterceptorInterface;
    ChangeDetectorInterceptor?: new (
      name?: string,
      options?: ChangeDetectorConfig,
    ) => BaseInterceptorInterface;
    TabStreamer: any;
  }
}

// CaptchaDetectorCallback
export type CaptchaDetectorCallback = (
  rgbaBuffer: Uint8Array,
  dimensions: { width: number; height: number },
) => Promise<void>;

/**
 * Persistent CDP Controller interface - handles CDP operations in control tabs
 * Exposed on window.persistentCDPController
 */
export interface PersistentCDPController {
  /**
   * Initialize the persistent CDP controller
   */
  init(): Promise<void>;

  /**
   * Setup browser metrics and viewport configuration
   */
  setupBrowserMetrics(viewport: Viewport): Promise<void>;

  /**
   * Dispatch mouse movement event
   */
  dispatchMouseMove(x: number, y: number): Promise<void>;

  /**
   * Dispatch mouse down event
   */
  dispatchMouseDown(x: number, y: number): Promise<void>;

  /**
   * Dispatch mouse up event
   */
  dispatchMouseUp(x: number, y: number): Promise<void>;

  /**
   * Dispatch mouse click event
   */
  dispatchMouseClick(x: number, y: number): Promise<void>;

  /**
   * Dispatch keyboard event
   */
  dispatchKeyEvent(eventData: InputEventData): Promise<void>;

  /**
   * Insert text at current cursor position
   */
  insertText(text: string): Promise<void>;

  /**
   * Take a screenshot of the current page
   */
  takeScreenshot(): Promise<ScreenshotResult>;

  /**
   * Capture screenshot with grayscale conversion applied
   */
  captureScreenshotWithGrayscale(): Promise<ScreenshotResult>;

  /**
   * Request a new frame generation
   */
  requestNewFrame(): Promise<void>;

  /**
   * Trigger mouse movement to generate UI updates
   */
  triggerMouseMovement(): Promise<void>;

  /**
   * Test connectivity
   */
  ping(): Promise<string>;

  /**
   * Redact PII from the page
   */
  redactPII(): Promise<void>;

  /**
   * Restore PII on the page
   */
  restorePII(): Promise<void>;
}

/**
 * Cross Tab Communicator interface - handles communication between tabs
 * Exposed on window.crossTabCommunicator
 */
export interface CrossTabCommunicator {
  /**
   * Send a message to another tab
   */
  sendMessage(type: string, data?: any): Promise<any>;

  /**
   * Set up message listener
   */
  onMessage(callback: (message: any) => void): void;

  /**
   * Clean up resources
   */
  cleanup(): void;
}

/**
 * Screen Cropper interface for direct client-side usage
 * This represents the actual implementation in the browser context
 */
export interface ScreenCropperClient {
  /**
   * Initialize screen cropper with WebSocket and WebRTC connections
   * Sets up connections but doesn't start streaming
   */
  init(wsEndpoint: string, viewport: Viewport): Promise<string>;

  /**
   * Start screen cropper streaming with captcha detection
   */
  start(viewport: Viewport): Promise<void>;

  /**
   * Stop streaming and clean up resources
   */
  stopStreaming(): void;

  /**
   * Update the crop box for focused capture
   */
  updateCropBox(cropBox: BoundingBox): void;

  /**
   * Start capturing frames for captcha detector
   */
  startCapturingForCaptchaDetector(): void;

  /**
   * Stop capturing frames for captcha detector
   */
  stopCapturingForCaptchaDetector(): void;

  /**
   * Pause frame sending temporarily
   */
  pauseFrameSending(): void;

  /**
   * Resume frame sending
   */
  resumeFrameSending(): void;

  /**
   * Register a callback for captcha detector to receive frames
   */
  registerCaptchaDetectorCallback(callback: CaptchaDetectorCallback): void;
}

/**
 * Captcha Detector interface for direct client-side usage
 * This represents the actual implementation in the browser context
 */
export interface CaptchaDetectorClient {
  /**
   * Initialize the captcha detector with configuration
   */
  initialize(options?: CaptchaDetectorConfig): void;

  /**
   * Clean up resources and event listeners
   */
  cleanup(): void;

  /**
   * Trigger screenshot comparison manually
   */
  triggerScreenshotComparison(): Promise<ComparisonResult>;

  /**
   * Get current configuration
   */
  getConfig(): CaptchaDetectorConfig;

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<CaptchaDetectorConfig>): void;
}

export interface TensorFlowCaptchaDetector {
  initialize: (options?: Config) => Promise<boolean>;

  detectObjects: (rgbaBuffer: Uint8Array, dimensions: Viewport) => Promise<DetectionResults>;

  findBestCaptchaBoundingBox: (detections: Detection[]) => BoundingBox | null;

  takeScreenshotAndDetect: (dimensions: Viewport) => Promise<DetectionResults>;

  getInitialBoundingBox: (dimensions: Viewport) => Promise<BoundingBox>;

  refreshBoundingBox: (dimensions: Viewport) => Promise<void>;
}

export interface ScreenshotComparisonUtils {
  compareScreenshots: (
    img1: IImageData,
    img2: IImageData,
    options?: ScreenshotComparisonOptions,
  ) => ComparisonResult;
  prepareBufferForComparison: (buffer: Uint8Array, width: number, height: number) => IImageData;
  createTestImage: (width: number, height: number, color?: number[]) => IImageData;
  createImageWithDifferences: (
    baseImage: IImageData,
    diffPixelCount: number,
    diffColor?: number[],
  ) => IImageData;
}

export interface BrowserController {
  init(): Promise<void>;
  takeScreenshot(enableGrayscale: boolean, quality?: number): Promise<ScreenshotResult>;
  dispatchMouseMove(x: number, y: number): Promise<void>;
  dispatchMouseDown(x: number, y: number): Promise<void>;
  dispatchMouseUp(x: number, y: number): Promise<void>;
  dispatchMouseClick(x: number, y: number): Promise<void>;
  dispatchKeyEvent(eventData: InputEventData): Promise<void>;
  insertText(text: string): Promise<void>;
  setupBrowserMetrics(viewport: Viewport): Promise<void>;
  requestNewFrame(): Promise<void>;
  triggerMouseMovement(): Promise<void>;
  ping(): Promise<string>;
  handleInputEvent(event: any): Promise<void>;
}

export interface InterceptorConfig {
  debug?: boolean;
  enabled?: boolean;
  [key: string]: any;
}

export interface BrightnessConfig extends InterceptorConfig {
  brightness?: number;
}

export interface BlurConfig extends InterceptorConfig {
  blurRadius?: number;
}

export interface InterceptorStats {
  framesProcessed: number;
  errorsEncountered: number;
  averageProcessingTime: number;
  lastProcessingTime: number;
  totalProcessingTime: number;
}

export interface BaseInterceptorInterface {
  readonly name: string;
  readonly type: string;
  readonly isInitialized: boolean;
  readonly isEnabled: boolean;
  readonly config: InterceptorConfig;
  readonly stats: InterceptorStats;

  initialize(videoTrack?: MediaStreamTrack): Promise<MediaStreamTrack>;
  processFrame(
    frame: VideoFrame,
    controller: TransformStreamDefaultController<VideoFrame>,
  ): Promise<void>;
  processVideoFrame(frame: VideoFrame): Promise<VideoFrame>;
  updateConfig(newConfig: Partial<InterceptorConfig>): void;
  cleanup(): Promise<void>;
  enable(): void;
  disable(): void;
  getStats(): InterceptorStats;
  log(...args: any[]): void;
}

// Specific Interceptor Configs
export interface VideoCropConfig extends InterceptorConfig {
  enableCropping?: boolean;
  cropRegion?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  defaultCropRegion?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  frameRate?: number;
}

export interface ChangeDetectorConfig extends InterceptorConfig {
  changeThreshold?: number;
  stabilityThreshold?: number;
  consecutiveStableFrames?: number;
  maxWaitDuration?: number;
  comparisonInterval?: number;
  pixelSampling?: number;
}

export interface InterceptorConstructor {
  new (name: string, config?: InterceptorConfig): BaseInterceptorInterface;
}

export interface InterceptorRegistryInterface {
  register(name: string, interceptorClass: any, defaultConfig: any): void;
  getRegisteredInterceptors(): string[];
  setClientConfiguration(
    webClientId: string,
    interceptorNames: string[],
    interceptorConfigs: Record<string, any>,
  ): void;
  createClientInterceptors(webClientId: string): BaseInterceptorInterface[];
}

export interface InterceptorPipelineInterface {
  readonly interceptors: BaseInterceptorInterface[];
  readonly isInitialized: boolean;
  readonly isEnabled: boolean;
  readonly updateInterceptorConfigs: (configs: Record<string, any>) => void;
  readonly stats: {
    framesProcessed: number;
    totalProcessingTime: number;
    averageProcessingTime: number;
    interceptorStats: Map<
      string,
      {
        framesProcessed: number;
        totalProcessingTime: number;
        averageProcessingTime: number;
      }
    >;
  };

  initialize(videoTrack: MediaStreamTrack): Promise<MediaStreamTrack>;
  addInterceptor(interceptor: BaseInterceptorInterface): void;
  removeInterceptor(name: string): boolean;
  updateInterceptorConfig(name: string, config: Partial<InterceptorConfig>): boolean;
  enable(): void;
  disable(): void;
  cleanup(): Promise<void>;
}

export interface CDPManager {
  addConnection(targetTabId: string, targetInfo?: any): Promise<any>;
  removeConnection(targetTabId: string): Promise<void>;
  getConnection(targetTabId: string): any;
  getAllConnections(): Map<string, any>;
  executeCommand(
    targetTabId: string,
    method: string,
    params?: Record<string, unknown>,
  ): Promise<unknown>;
  getTargetTabInfo(targetTabId: string): Promise<any>;
  executeScript(targetTabId: string, script: string): Promise<unknown>;
  registerEventHandler(eventType: string, handler: any): void;
  unregisterEventHandler(eventType: string): void;
  handleUserEvent(userEvent: any, targetTabId: string): Promise<void>;
  initializeDefaultHandlers(): void;
  cleanup(): Promise<void>;
  getStats(): any;
}
